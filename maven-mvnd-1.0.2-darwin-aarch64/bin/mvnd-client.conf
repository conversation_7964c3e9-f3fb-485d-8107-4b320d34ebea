# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
main is org.mvndaemon.mvnd.client.DefaultClient from plexus.core

set maven.home default ${mvnd.home}/mvn
set maven.conf default ${maven.home}/conf

set java.util.logging.config.file default ${maven.conf}/logging/java.util.logging.properties

[plexus.core]
load       ${maven.conf}/logging
load       ${maven.home}/lib/mvnd/*.jar
optionally ${maven.home}/lib/ext/redisson/*.jar
optionally ${maven.home}/lib/ext/hazelcast/*.jar
optionally ${maven.home}/lib/ext/*.jar
load       ${maven.home}/lib/maven-*.jar
load       ${maven.home}/lib/*.jar
