#
#     Copyright 2020 the original author or authors.
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.
#

#
# This file contains the properties that can be configured through properties file.
# Note that mvnd read properties file from the following locations:
#  - the supplied properties path
#       through the MVND_PROPERTIES_PATH environment variable or
#       through the mvnd.propertiesPath system variable
#  - the local properties path
#       located at [PROJECT_HOME]/.mvn/mvnd.properties
#  - the user properties path
#       located at [USER_HOME]/.m2/mvnd.properties
#  - the system properties path
#       located at [MVND_HOME]/conf/mvnd.properties
# Properties defined in the first files will take precedence over properties
# specified in a lower ranked file.
#
# A few special properties do not follow the above mechanism:
#   - mvnd.daemonStorage: this property defines the location where mvnd stores its
#        files (registry and daemon logs).  This property can only be defined as
#        a system property on the command line
#   - mvnd.id: this property is used internally to identify the daemon being created
#   - mvnd.extClasspath: internal option to specify the maven extension classpath
#   - mvnd.coreExtensions: internal option to specify the list of maven extension to register
#

# MVND_NO_BUFFERING
# Property that can be set to avoid buffering the output and display events continuously,
# closer to the usual maven display. Passing {@code -B} or {@code --batch-mode} on the 
# command line enables this too for the given build.
#
# mvnd.noBuffering = false

# MVND_ROLLING_WINDOW_SIZE
# The number of log lines to display for each Maven module that is built in parallel.
#
# mvnd.rollingWindowSize = 0

# MVND_LOG_PURGE_PERIOD
# The automatic log purge period.
#
# mvnd.logPurgePeriod = 7d

# MVND_NO_DAEMON
# Property to disable using a daemon (usefull for debugging, and only available in non native mode).
#
# mvnd.noDaemon = false

# MVND_DEBUG
# Property to launch the daemon in debug mode with the following JVM argument
# <code>-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=8000</code>
#
# mvnd.debug = false

# MVND_IDLE_TIMEOUT
# Duration after which an unused daemon will shut down.
#
# mvnd.idleTimeout = 3 hours

# MVND_KEEP_ALIVE
# Time after which a daemon will send a keep-alive message to the client if the current build
# has produced no output.
#
# mvnd.keepAlive = 100 ms

# MVND_MAX_LOST_KEEP_ALIVE
# The maximum number of keep alive message that can be lost before the client considers the daemon
# as having had a failure.
#
# mvnd.maxLostKeepAlive = 30

# MVND_MIN_THREADS
# The minimum number of threads to use when constructing the default {@code -T} parameter for the daemon.
# This value is ignored if @{@code -T}, @{@code --threads} or {@code -Dmvnd.threads} is specified on the command
# line, or if {@code mvnd.threads} is specified in {@code ~/.m2/mvnd.properties}.
#
# mvnd.minThreads = 1

# MVND_THREADS
# The number of threads to pass to the daemon; same syntax as Maven's {@code -T}/{@code --threads} option. Ignored
# if the user passes @{@code -T}, @{@code --threads} or {@code -Dmvnd.threads} on the command
# line.
#
# mvnd.threads =

# MVND_BUILDER
# The maven builder name to use. Ignored if the user passes
# {@code -b} or {@code --builder} on the command line
#
# mvnd.builder = smart

# MVND_MIN_HEAP_SIZE
# JVM options for the daemon to specify the starting heap size
##
# mvnd.minHeapSize = 128M

# MVND_MAX_HEAP_SIZE
# JVM options for the daemon to specify the maximum heap size
#
# mvnd.maxHeapSize = 2G

# MVND_THREAD_STACK_SIZE
# JVM options for the daemon to specify the thread stack size
#
# mvnd.threadStackSize = 1M

# MVND_JVM_ARGS
# Additional JVM args for the daemon
#
# mvnd.jvmArgs =

# MVND_ENABLE_ASSERTIONS
# JVM options for the daemon to enable assertions
#
# mvnd.enableAssertions = false

# MVND_EXPIRATION_CHECK_DELAY
# Interval to check if the daemon should expire
#
# mvnd.expirationCheckDelay = 10 seconds

# MVND_DUPLICATE_DAEMON_GRACE_PERIOD
# Period after which idle daemons will shut down
#
# mvnd.duplicateDaemonGracePeriod = 10 seconds

# MVND_HOME
# The daemon installation directory. The client normally sets this according to where its mvnd executable is located
#
# mvnd.home=<path>

# JAVA_HOME
# Java home for starting the daemon. The client normally sets this as environment variable: JAVA_HOME
#
# java.home=<path>

# 
# The location of the maven settings file. The client normally uses default settings in {@code ~/.m2/settings.xml}.
# maven.settings=<path>
