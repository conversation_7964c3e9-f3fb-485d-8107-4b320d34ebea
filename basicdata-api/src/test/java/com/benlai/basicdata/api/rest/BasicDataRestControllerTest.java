package com.benlai.basicdata.api.rest;

import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.given;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;

@QuarkusTest
public class BasicDataRestControllerTest {

    @Test
    public void testCreateAndGetBasicData() {
        // Create basic data
        String createRequest = """
            {
                "name": "Test Data",
                "description": "Test Description",
                "category": "TEST"
            }
            """;

        String id = given()
                .contentType(ContentType.JSON)
                .body(createRequest)
                .when()
                .post("/api/v1/basicdata")
                .then()
                .statusCode(201)
                .body("id", notNullValue())
                .body("name", is("Test Data"))
                .body("category", is("TEST"))
                .extract()
                .path("id");

        // Get basic data
        given()
                .when()
                .get("/api/v1/basicdata/" + id)
                .then()
                .statusCode(200)
                .body("id", is(id))
                .body("name", is("Test Data"))
                .body("category", is("TEST"));
    }

    @Test
    public void testListBasicData() {
        given()
                .when()
                .get("/api/v1/basicdata")
                .then()
                .statusCode(200)
                .body("data", notNullValue())
                .body("total", notNullValue())
                .body("page", is(0))
                .body("size", is(10));
    }

    @Test
    public void testGetNonExistentBasicData() {
        given()
                .when()
                .get("/api/v1/basicdata/non-existent-id")
                .then()
                .statusCode(404);
    }
}
