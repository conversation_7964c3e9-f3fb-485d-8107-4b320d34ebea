package com.benlai.basicdata.api.rest;

import com.benlai.basicdata.api.dto.BasicDataDto;
import com.benlai.basicdata.api.dto.CreateBasicDataRequest;
import com.benlai.basicdata.api.dto.UpdateBasicDataRequest;
import com.benlai.basicdata.api.dto.ListBasicDataResponse;
import com.benlai.basicdata.api.service.BasicDataService;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.ApiResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

@Path("/api/v1/basicdata")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Basic Data", description = "Basic Data REST API")
public class BasicDataRestController {

    @Inject
    BasicDataService basicDataService;

    @GET
    @Path("/{id}")
    @Operation(summary = "Get basic data by ID")
    @ApiResponse(responseCode = "200", description = "Basic data found")
    @ApiResponse(responseCode = "404", description = "Basic data not found")
    public Response getBasicData(
            @Parameter(description = "Basic data ID") 
            @PathParam("id") @NotBlank String id) {
        try {
            BasicDataDto data = basicDataService.getBasicData(id);
            return Response.ok(data).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(new ErrorResponse("Basic data not found"))
                    .build();
        }
    }

    @POST
    @Operation(summary = "Create new basic data")
    @ApiResponse(responseCode = "201", description = "Basic data created")
    @ApiResponse(responseCode = "400", description = "Invalid request")
    public Response createBasicData(@Valid CreateBasicDataRequest request) {
        try {
            BasicDataDto data = basicDataService.createBasicData(request);
            return Response.status(Response.Status.CREATED).entity(data).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(new ErrorResponse(e.getMessage()))
                    .build();
        }
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "Update existing basic data")
    @ApiResponse(responseCode = "200", description = "Basic data updated")
    @ApiResponse(responseCode = "404", description = "Basic data not found")
    public Response updateBasicData(
            @Parameter(description = "Basic data ID") 
            @PathParam("id") @NotBlank String id,
            @Valid UpdateBasicDataRequest request) {
        try {
            BasicDataDto data = basicDataService.updateBasicData(id, request);
            return Response.ok(data).build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(new ErrorResponse("Basic data not found"))
                    .build();
        }
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "Delete basic data")
    @ApiResponse(responseCode = "204", description = "Basic data deleted")
    @ApiResponse(responseCode = "404", description = "Basic data not found")
    public Response deleteBasicData(
            @Parameter(description = "Basic data ID") 
            @PathParam("id") @NotBlank String id) {
        try {
            basicDataService.deleteBasicData(id);
            return Response.noContent().build();
        } catch (IllegalArgumentException e) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(new ErrorResponse("Basic data not found"))
                    .build();
        }
    }

    @GET
    @Operation(summary = "List all basic data")
    @ApiResponse(responseCode = "200", description = "Basic data list")
    public Response listBasicData(
            @Parameter(description = "Page number") 
            @QueryParam("page") @DefaultValue("0") int page,
            @Parameter(description = "Page size") 
            @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "Category filter") 
            @QueryParam("category") String category) {
        ListBasicDataResponse response = basicDataService.listBasicData(page, size, category);
        return Response.ok(response).build();
    }

    public static class ErrorResponse {
        public String message;

        public ErrorResponse(String message) {
            this.message = message;
        }
    }
}
