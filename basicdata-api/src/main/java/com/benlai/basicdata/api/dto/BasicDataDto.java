package com.benlai.basicdata.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record BasicDataDto(
    @JsonProperty("id") String id,
    @JsonProperty("name") String name,
    @JsonProperty("description") String description,
    @JsonProperty("category") String category,
    @JsonProperty("created_time") long createdTime,
    @JsonProperty("updated_time") long updatedTime
) {}
