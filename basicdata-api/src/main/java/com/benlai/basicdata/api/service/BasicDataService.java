package com.benlai.basicdata.api.service;

import com.benlai.basicdata.api.dto.BasicDataDto;
import com.benlai.basicdata.api.dto.CreateBasicDataRequest;
import com.benlai.basicdata.api.dto.UpdateBasicDataRequest;
import com.benlai.basicdata.api.dto.ListBasicDataResponse;

import jakarta.enterprise.context.ApplicationScoped;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@ApplicationScoped
public class BasicDataService {

    // 简单的内存存储，实际项目中应该使用数据库
    private final Map<String, BasicDataDto> dataStore = new ConcurrentHashMap<>();

    public BasicDataDto getBasicData(String id) {
        BasicDataDto data = dataStore.get(id);
        if (data == null) {
            throw new IllegalArgumentException("Basic data not found with id: " + id);
        }
        return data;
    }

    public BasicDataDto createBasicData(CreateBasicDataRequest request) {
        if (request.name() == null || request.name().trim().isEmpty()) {
            throw new IllegalArgumentException("Name is required");
        }
        if (request.category() == null || request.category().trim().isEmpty()) {
            throw new IllegalArgumentException("Category is required");
        }

        String id = UUID.randomUUID().toString();
        long currentTime = Instant.now().toEpochMilli();
        
        BasicDataDto data = new BasicDataDto(
            id,
            request.name().trim(),
            request.description() != null ? request.description().trim() : "",
            request.category().trim(),
            currentTime,
            currentTime
        );
        
        dataStore.put(id, data);
        return data;
    }

    public BasicDataDto updateBasicData(String id, UpdateBasicDataRequest request) {
        BasicDataDto existing = dataStore.get(id);
        if (existing == null) {
            throw new IllegalArgumentException("Basic data not found with id: " + id);
        }

        long currentTime = Instant.now().toEpochMilli();
        
        BasicDataDto updated = new BasicDataDto(
            existing.id(),
            request.name() != null ? request.name().trim() : existing.name(),
            request.description() != null ? request.description().trim() : existing.description(),
            request.category() != null ? request.category().trim() : existing.category(),
            existing.createdTime(),
            currentTime
        );
        
        dataStore.put(id, updated);
        return updated;
    }

    public void deleteBasicData(String id) {
        BasicDataDto removed = dataStore.remove(id);
        if (removed == null) {
            throw new IllegalArgumentException("Basic data not found with id: " + id);
        }
    }

    public ListBasicDataResponse listBasicData(int page, int size, String category) {
        List<BasicDataDto> allData = new ArrayList<>(dataStore.values());
        
        // 按类别过滤
        if (category != null && !category.trim().isEmpty()) {
            allData = allData.stream()
                .filter(data -> category.equals(data.category()))
                .collect(Collectors.toList());
        }
        
        // 按创建时间排序（最新的在前）
        allData.sort((a, b) -> Long.compare(b.createdTime(), a.createdTime()));
        
        int total = allData.size();
        int startIndex = page * size;
        int endIndex = Math.min(startIndex + size, total);
        
        List<BasicDataDto> pageData = startIndex < total ? 
            allData.subList(startIndex, endIndex) : 
            Collections.emptyList();
        
        return new ListBasicDataResponse(pageData, total, page, size);
    }
}
