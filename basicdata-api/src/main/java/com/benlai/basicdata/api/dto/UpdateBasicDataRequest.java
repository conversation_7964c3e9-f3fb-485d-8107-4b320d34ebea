package com.benlai.basicdata.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;

public record UpdateBasicDataRequest(
    @JsonProperty("name") 
    @Size(max = 100, message = "Name must not exceed 100 characters")
    String name,
    
    @JsonProperty("description") 
    @Size(max = 500, message = "Description must not exceed 500 characters")
    String description,
    
    @JsonProperty("category") 
    @Size(max = 50, message = "Category must not exceed 50 characters")
    String category
) {}
