package com.benlai.basicdata.api.grpc;

import com.benlai.basicdata.api.dto.BasicDataDto;
import com.benlai.basicdata.api.dto.CreateBasicDataRequest;
import com.benlai.basicdata.api.dto.UpdateBasicDataRequest;
import com.benlai.basicdata.api.dto.ListBasicDataResponse;
import com.benlai.basicdata.api.service.BasicDataService;
import com.benlai.basicdata.grpc.*;

import io.quarkus.grpc.GrpcService;
import io.smallrye.mutiny.Uni;

import jakarta.inject.Inject;

@GrpcService
public class BasicDataGrpcService implements BasicDataService {

    @Inject
    com.benlai.basicdata.api.service.BasicDataService basicDataService;

    @Override
    public Uni<BasicDataResponse> getBasicData(GetBasicDataRequest request) {
        return Uni.createFrom().item(() -> {
            try {
                BasicDataDto dto = basicDataService.getBasicData(request.getId());
                return convertToGrpcResponse(dto);
            } catch (IllegalArgumentException e) {
                throw new io.grpc.StatusRuntimeException(
                    io.grpc.Status.NOT_FOUND.withDescription("Basic data not found")
                );
            }
        });
    }

    @Override
    public Uni<BasicDataResponse> createBasicData(CreateBasicDataRequest request) {
        return Uni.createFrom().item(() -> {
            try {
                com.benlai.basicdata.api.dto.CreateBasicDataRequest dto = 
                    new com.benlai.basicdata.api.dto.CreateBasicDataRequest(
                        request.getName(),
                        request.getDescription(),
                        request.getCategory()
                    );
                BasicDataDto result = basicDataService.createBasicData(dto);
                return convertToGrpcResponse(result);
            } catch (IllegalArgumentException e) {
                throw new io.grpc.StatusRuntimeException(
                    io.grpc.Status.INVALID_ARGUMENT.withDescription(e.getMessage())
                );
            }
        });
    }

    @Override
    public Uni<BasicDataResponse> updateBasicData(UpdateBasicDataRequest request) {
        return Uni.createFrom().item(() -> {
            try {
                com.benlai.basicdata.api.dto.UpdateBasicDataRequest dto = 
                    new com.benlai.basicdata.api.dto.UpdateBasicDataRequest(
                        request.getName(),
                        request.getDescription(),
                        request.getCategory()
                    );
                BasicDataDto result = basicDataService.updateBasicData(request.getId(), dto);
                return convertToGrpcResponse(result);
            } catch (IllegalArgumentException e) {
                throw new io.grpc.StatusRuntimeException(
                    io.grpc.Status.NOT_FOUND.withDescription("Basic data not found")
                );
            }
        });
    }

    @Override
    public Uni<DeleteBasicDataResponse> deleteBasicData(DeleteBasicDataRequest request) {
        return Uni.createFrom().item(() -> {
            try {
                basicDataService.deleteBasicData(request.getId());
                return DeleteBasicDataResponse.newBuilder()
                    .setSuccess(true)
                    .setMessage("Basic data deleted successfully")
                    .build();
            } catch (IllegalArgumentException e) {
                throw new io.grpc.StatusRuntimeException(
                    io.grpc.Status.NOT_FOUND.withDescription("Basic data not found")
                );
            }
        });
    }

    @Override
    public Uni<ListBasicDataResponse> listBasicData(ListBasicDataRequest request) {
        return Uni.createFrom().item(() -> {
            com.benlai.basicdata.api.dto.ListBasicDataResponse dto = 
                basicDataService.listBasicData(
                    request.getPage(), 
                    request.getSize(), 
                    request.getCategory().isEmpty() ? null : request.getCategory()
                );
            
            ListBasicDataResponse.Builder builder = ListBasicDataResponse.newBuilder()
                .setTotal(dto.total())
                .setPage(dto.page())
                .setSize(dto.size());
            
            dto.data().forEach(item -> builder.addData(convertToGrpcResponse(item)));
            
            return builder.build();
        });
    }

    private BasicDataResponse convertToGrpcResponse(BasicDataDto dto) {
        return BasicDataResponse.newBuilder()
            .setId(dto.id())
            .setName(dto.name())
            .setDescription(dto.description())
            .setCategory(dto.category())
            .setCreatedTime(dto.createdTime())
            .setUpdatedTime(dto.updatedTime())
            .build();
    }
}
