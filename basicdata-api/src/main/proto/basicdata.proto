syntax = "proto3";

package com.benlai.basicdata.grpc;

option java_multiple_files = true;
option java_package = "com.benlai.basicdata.grpc";
option java_outer_classname = "BasicDataProto";

// Basic Data Service
service BasicDataService {
  // Get basic data by ID
  rpc GetBasicData(GetBasicDataRequest) returns (BasicDataResponse);
  
  // Create new basic data
  rpc CreateBasicData(CreateBasicDataRequest) returns (BasicDataResponse);
  
  // Update existing basic data
  rpc UpdateBasicData(UpdateBasicDataRequest) returns (BasicDataResponse);
  
  // Delete basic data
  rpc DeleteBasicData(DeleteBasicDataRequest) returns (DeleteBasicDataResponse);
  
  // List all basic data
  rpc ListBasicData(ListBasicDataRequest) returns (ListBasicDataResponse);
}

// Request messages
message GetBasicDataRequest {
  string id = 1;
}

message CreateBasicDataRequest {
  string name = 1;
  string description = 2;
  string category = 3;
}

message UpdateBasicDataRequest {
  string id = 1;
  string name = 2;
  string description = 3;
  string category = 4;
}

message DeleteBasicDataRequest {
  string id = 1;
}

message ListBasicDataRequest {
  int32 page = 1;
  int32 size = 2;
  string category = 3;
}

// Response messages
message BasicDataResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string category = 4;
  int64 created_time = 5;
  int64 updated_time = 6;
}

message DeleteBasicDataResponse {
  bool success = 1;
  string message = 2;
}

message ListBasicDataResponse {
  repeated BasicDataResponse data = 1;
  int32 total = 2;
  int32 page = 3;
  int32 size = 4;
}
