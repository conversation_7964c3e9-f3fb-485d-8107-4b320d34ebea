# Quarkus Configuration
quarkus:
  application:
    name: basicdata-api
    version: 1.0.0-SNAPSHOT
  
  # HTTP Configuration
  http:
    port: 8080
    cors:
      ~: true
      origins: "*"
      methods: "GET,POST,PUT,DELETE,OPTIONS"
      headers: "Content-Type,Authorization"
  
  # gRPC Configuration
  grpc:
    server:
      port: 9090
      enable-reflection-service: true
  
  # OpenAPI Configuration
  smallrye-openapi:
    info-title: BasicData API
    info-version: 1.0.0
    info-description: REST and gRPC API for Basic Data Management
    info-contact-name: Development Team
    path: /openapi
  
  # Swagger UI Configuration
  swagger-ui:
    always-include: true
    path: /swagger-ui

# Logging Configuration
quarkus.log:
  level: INFO
  console:
    enable: true
    format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"
  category:
    "com.benlai.basicdata":
      level: DEBUG

# Development Profile
"%dev":
  quarkus:
    log:
      level: DEBUG
    grpc:
      server:
        enable-reflection-service: true

# Test Profile  
"%test":
  quarkus:
    http:
      port: 8081
    grpc:
      server:
        port: 9091
